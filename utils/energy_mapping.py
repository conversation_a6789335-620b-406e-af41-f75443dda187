"""
能耗数据映射工具模块
处理3个能耗数据源到6台空调的映射关系
"""
import numpy as np


class EnergyMappingHandler:
    """
    能耗数据映射处理器
    
    映射关系：
    - 艾特网能空调1# → 能耗数据源1 (df07a719_f112_494f_ad57_910c6d3466aa)
    - 艾特网能空调2# + 美的空调 → 能耗数据源2 (c413bdb5_de18_4f1c_ba6f_c2ac6a7d0b46)  
    - 维谛精密空调1+2+3 → 能耗数据源3 (ae51067a_0ac3_4970_a0a9_386f8acc33cc)
    """
    
    def __init__(self, uid_config, data_precision=np.float16):
        self.uid_config = uid_config
        self.data_precision = data_precision
        
        # 空调名称映射
        self.ac_names = uid_config['device_uid']['air_conditioner_name']
        
        # 能耗数据源映射关系
        self.energy_mapping = {
            # 艾特网能空调1# → 能耗数据源1
            0: [0],  # 空调索引0 → 能耗源0
            # 艾特网能空调2# + 美的空调 → 能耗数据源2
            1: [1], 2: [1],  # 空调索引1,2 → 能耗源1
            # 维谛精密空调1+2+3 → 能耗数据源3
            3: [2], 4: [2], 5: [2]  # 空调索引3,4,5 → 能耗源2
        }
        
        # 反向映射：能耗源到空调列表
        self.reverse_mapping = {
            0: [0],        # 能耗源0 → 空调0
            1: [1, 2],     # 能耗源1 → 空调1,2
            2: [3, 4, 5]   # 能耗源2 → 空调3,4,5
        }
        
    def map_energy_to_ac(self, energy_data):
        """
        将3个能耗数据源映射到6台空调
        
        Args:
            energy_data: 长度为3的能耗数据列表
            
        Returns:
            长度为6的空调能耗数据列表
        """
        if len(energy_data) != 3:
            raise ValueError(f"能耗数据应包含3个值，实际收到{len(energy_data)}个")
            
        ac_energy = [self.data_precision(0)] * 6
        
        for energy_idx, energy_value in enumerate(energy_data):
            # 获取该能耗源对应的空调列表
            ac_indices = self.reverse_mapping[energy_idx]
            
            if len(ac_indices) == 1:
                # 单台空调直接映射
                ac_energy[ac_indices[0]] = self.data_precision(energy_value)
            else:
                # 多台空调需要分配能耗
                distributed_energy = self._distribute_energy(energy_value, ac_indices)
                for i, ac_idx in enumerate(ac_indices):
                    ac_energy[ac_idx] = self.data_precision(distributed_energy[i])
                    
        return ac_energy
    
    def _distribute_energy(self, total_energy, ac_indices):
        """
        将总能耗分配给多台空调
        
        Args:
            total_energy: 总能耗值
            ac_indices: 空调索引列表
            
        Returns:
            分配后的能耗列表
        """
        # 简单平均分配策略（后续可以根据实际情况优化）
        num_ac = len(ac_indices)
        avg_energy = total_energy / num_ac
        return [avg_energy] * num_ac
    
    def get_energy_source_for_ac(self, ac_index):
        """
        获取指定空调对应的能耗数据源索引
        
        Args:
            ac_index: 空调索引 (0-5)
            
        Returns:
            能耗数据源索引 (0-2)
        """
        if ac_index not in self.energy_mapping:
            raise ValueError(f"无效的空调索引: {ac_index}")
        return self.energy_mapping[ac_index][0]
    
    def get_ac_group_info(self):
        """
        获取空调分组信息
        
        Returns:
            字典，包含每个能耗源对应的空调组信息
        """
        group_info = {}
        for energy_idx, ac_indices in self.reverse_mapping.items():
            ac_names = [self.ac_names[i] for i in ac_indices]
            group_info[f"energy_source_{energy_idx}"] = {
                "ac_indices": ac_indices,
                "ac_names": ac_names,
                "count": len(ac_indices)
            }
        return group_info
    
    def validate_mapping(self):
        """
        验证映射关系的完整性和正确性
        
        Returns:
            bool: 验证是否通过
        """
        # 检查所有空调都有映射
        mapped_acs = set()
        for ac_indices in self.reverse_mapping.values():
            mapped_acs.update(ac_indices)
        
        expected_acs = set(range(6))
        if mapped_acs != expected_acs:
            return False
            
        # 检查映射的一致性
        for ac_idx in range(6):
            energy_src = self.get_energy_source_for_ac(ac_idx)
            if ac_idx not in self.reverse_mapping[energy_src]:
                return False
                
        return True


def create_energy_mapping_handler(uid_config, data_precision=np.float16):
    """
    创建能耗映射处理器的工厂函数
    
    Args:
        uid_config: UID配置字典
        data_precision: 数据精度类型
        
    Returns:
        EnergyMappingHandler实例
    """
    handler = EnergyMappingHandler(uid_config, data_precision)
    
    # 验证映射关系
    if not handler.validate_mapping():
        raise ValueError("能耗映射关系验证失败")
        
    return handler
